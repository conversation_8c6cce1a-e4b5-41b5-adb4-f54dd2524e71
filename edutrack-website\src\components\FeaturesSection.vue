<template>
  <section id="features" class="section features">
    <div class="container">
      <h2 class="section-title fade-in">المميزات الرئيسية</h2>
      
      <div class="features-grid">
        <div
          class="feature-card fade-in"
          v-for="(feature, index) in features"
          :key="index"
          :style="{ '--card-color': feature.color }"
        >
          <div class="feature-icon">
            <span v-html="feature.icon"></span>
          </div>
          <h3 class="feature-title">{{ feature.title }}</h3>
          <p class="feature-description">{{ feature.description }}</p>
          <ul class="feature-list" v-if="feature.items">
            <li v-for="item in feature.items" :key="item">{{ item }}</li>
          </ul>
        </div>
      </div>
      
      <!-- Security Section -->
      <div class="security-section fade-in">
        <div class="security-content">
          <div class="security-text">
            <h3 class="security-title">
              <span class="security-icon">🔒</span>
              الأمان والحماية المتقدمة
            </h3>
            <p class="security-description">
              نحن نأخذ أمان بياناتك على محمل الجد. يوفر EduTrack حماية متعددة المستويات لضمان سلامة معلوماتك التعليمية.
            </p>
            <div class="security-features">
              <div class="security-item">
                <span class="security-badge">🛡️</span>
                <span>تشفير AES-256</span>
              </div>
              <div class="security-item">
                <span class="security-badge">🔍</span>
                <span>التحقق من التكامل</span>
              </div>
              <div class="security-item">
                <span class="security-badge">🚫</span>
                <span>حماية من التلاعب</span>
              </div>
              <div class="security-item">
                <span class="security-badge">⏰</span>
                <span>إدارة الجلسات الآمنة</span>
              </div>
            </div>
          </div>
          <div class="security-visual">
            <div class="shield-animation">
              <div class="shield">
                <div class="shield-inner">🔒</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref } from 'vue'

const features = ref([
  {
    icon: `<svg viewBox="0 0 24 24" width="32" height="32">
      <path fill="currentColor" d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2M4 1h2v6h2.5l-3.5 7-3.5-7H4V1M16 13c1.11 0 2-.89 2-2s-.89-2-2-2-2 .89-2 2 .89 2 2 2m1 1.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5"/>
    </svg>`,
    title: 'إدارة المجموعات والطلاب',
    description: 'إنشاء وإدارة مجموعات تعليمية متعددة مع تتبع شامل لبيانات الطلاب',
    items: [
      'إنشاء مجموعات حسب المواد الدراسية',
      'تسجيل وتتبع بيانات الطلاب',
      'تنظيم الطلاب بشكل فعال'
    ],
    color: '#667eea'
  },
  {
    icon: `<svg viewBox="0 0 24 24" width="32" height="32">
      <path fill="currentColor" d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
    </svg>`,
    title: 'جدولة الدروس',
    description: 'إنشاء وإدارة جداول دراسية أسبوعية مع متابعة التقدم',
    items: [
      'جداول دراسية أسبوعية',
      'تتبع الدروس المكتملة والمتبقية',
      'عرض الدروس اليومية والقادمة'
    ],
    color: '#f093fb'
  },
  {
    icon: `<svg viewBox="0 0 24 24" width="32" height="32">
      <path fill="currentColor" d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
    </svg>`,
    title: 'تسجيل الحضور',
    description: 'نظام متطور لتسجيل ومتابعة حضور الطلاب مع تحليلات شاملة',
    items: [
      'تسجيل حضور سريع وسهل',
      'سجل الحضور التاريخي',
      'تحليل نسب الحضور والغياب'
    ],
    color: '#28a745'
  },
  {
    icon: `<svg viewBox="0 0 24 24" width="32" height="32">
      <path fill="currentColor" d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"/>
    </svg>`,
    title: 'إدارة المدفوعات',
    description: 'متابعة شاملة للمدفوعات الشهرية وحالة الدفع لكل طالب',
    items: [
      'تسجيل المدفوعات الشهرية',
      'متابعة حالة الدفع',
      'حساب الرسوم المستحقة'
    ],
    color: '#ffc107'
  },
  {
    icon: `<svg viewBox="0 0 24 24" width="32" height="32">
      <path fill="currentColor" d="M6 2c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8c1.1 0 2-.9 2-2V8l-6-6H6zm7 7V3.5L18.5 9H13z"/>
    </svg>`,
    title: 'النسخ الاحتياطي',
    description: 'حماية بياناتك مع نظام نسخ احتياطي متقدم واستعادة سهلة',
    items: [
      'إنشاء نسخ احتياطية تلقائية',
      'استعادة البيانات بسهولة',
      'حماية من فقدان البيانات'
    ],
    color: '#17a2b8'
  },
  {
    icon: `<svg viewBox="0 0 24 24" width="32" height="32">
      <path fill="currentColor" d="M13,2.05V5.08C16.39,5.57 19,8.47 19,12C19,12.9 18.82,13.75 18.5,14.54L21.12,16.07C21.68,14.83 22,13.45 22,12C22,6.82 18.05,2.55 13,2.05M12,19A7,7 0 0,1 5,12C5,8.47 7.61,5.57 11,5.08V2.05C5.94,2.55 2,6.81 2,12A10,10 0 0,0 12,22C15.3,22 18.23,20.39 20.05,17.91L17.45,16.38C16.17,18 14.21,19 12,19Z"/>
    </svg>`,
    title: 'تحسينات الأداء',
    description: 'تجربة سريعة وسلسة حتى على الأجهزة منخفضة المواصفات',
    items: [
      'تحسين استهلاك الذاكرة',
      'تحسين أداء الرسوميات',
      'سرعة تحميل واستجابة عالية'
    ],
    color: '#fd7e14'
  }
])
</script>

<style scoped>
.features {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
  overflow: hidden;
}

.features::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: linear-gradient(180deg, rgba(102, 126, 234, 0.05) 0%, transparent 100%);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 2.5rem;
  margin-bottom: 5rem;
  position: relative;
  z-index: 2;
  width: 100%;
}

.feature-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  padding: 3rem 2.5rem;
  border-radius: 24px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--card-color, linear-gradient(135deg, #667eea 0%, #764ba2 100%));
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.3);
}

.feature-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 2rem;
  background: linear-gradient(135deg, var(--card-color, #667eea) 0%, var(--card-color, #764ba2) 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.feature-title {
  font-size: 1.6rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #2c3e50;
  text-align: center;
  line-height: 1.3;
}

.feature-description {
  color: #6c757d;
  margin-bottom: 2rem;
  line-height: 1.7;
  text-align: center;
  font-size: 1.1rem;
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-list li {
  padding: 1rem 0;
  color: #495057;
  position: relative;
  padding-right: 2rem;
  font-weight: 500;
  transition: color 0.3s ease;
}

.feature-list li::before {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, var(--card-color, #28a745) 0%, var(--card-color, #20c997) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-list li::after {
  content: '✓';
  position: absolute;
  right: 6px;
  top: 50%;
  transform: translateY(-50%);
  color: white;
  font-size: 0.8rem;
  font-weight: bold;
}

.security-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 3rem;
  color: white;
  margin-top: 2rem;
}

.security-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 3rem;
  align-items: center;
}

.security-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.security-icon {
  font-size: 2.5rem;
}

.security-description {
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.security-features {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.security-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 1rem;
  border-radius: 10px;
  backdrop-filter: blur(10px);
}

.security-badge {
  font-size: 1.5rem;
}

.security-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.shield-animation {
  position: relative;
}

.shield {
  width: 120px;
  height: 120px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.shield-inner {
  font-size: 3rem;
  animation: glow 2s infinite alternate;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 20px rgba(255, 255, 255, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

@keyframes glow {
  from {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  }
  to {
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.8);
  }
}

@media (max-width: 768px) {
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .security-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .security-features {
    grid-template-columns: 1fr;
  }
  
  .security-section {
    padding: 2rem;
  }
}
</style>
