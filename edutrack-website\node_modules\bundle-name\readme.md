# bundle-name

> Get [bundle name](https://developer.apple.com/library/Mac/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html#//apple_ref/doc/plist/info/CFBundleName) from a [bundle identifier](https://developer.apple.com/library/Mac/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html#//apple_ref/doc/plist/info/CFBundleIdentifier) (macOS): `com.apple.Safari` → `Safari`

## Install

```sh
npm install bundle-name
```

## Usage

```js
import bundleName from 'bundle-name';

console.log(await bundleName('com.apple.Safari'));
//=> 'Safari'
```

## Related

- [bundle-name-cli](https://github.com/sindresorhus/bundle-name-cli) - CLI for this module
- [bundle-id](https://github.com/sindresorhus/bundle-id) - Get bundle identifier from a bundle name
