{"name": "human-signals", "version": "8.0.1", "type": "module", "exports": {"types": "./build/src/main.d.ts", "default": "./build/src/main.js"}, "main": "./build/src/main.js", "types": "./build/src/main.d.ts", "files": ["build/src/**/*.{js,json,d.ts}", "!build/src/**/*.test.js", "!build/src/{helpers,fixtures}"], "sideEffects": false, "scripts": {"test": "gulp test"}, "description": "Human-friendly process signals", "keywords": ["signal", "signals", "handlers", "error-handling", "interrupts", "sigterm", "sigint", "irq", "process", "exit", "exit-code", "status", "operating-system", "es6", "javascript", "typescript", "linux", "macos", "windows", "nodejs"], "license": "Apache-2.0", "homepage": "https://www.github.com/ehmicky/human-signals", "repository": {"type": "git", "url": "git+https://github.com/ehmicky/human-signals.git"}, "bugs": {"url": "https://github.com/ehmicky/human-signals/issues"}, "author": "ehmicky <<EMAIL>> (https://github.com/ehmicky)", "directories": {"lib": "src"}, "devDependencies": {"@ehmicky/dev-tasks": "^3.0.33", "@ehmicky/eslint-config": "^20.0.31", "@ehmicky/prettier-config": "^1.0.6", "ajv": "^8.17.1", "test-each": "^7.0.1"}, "engines": {"node": ">=18.18.0"}}