{"name": "@vue/babel-plugin-resolve-type", "version": "1.4.0", "description": "Babel plugin for resolving Vue types.", "author": "三咲智子 <PERSON> <<EMAIL>>", "funding": "https://github.com/sponsors/sxzz", "homepage": "https://github.com/vuejs/babel-plugin-jsx/tree/dev/packages/babel-plugin-resolve-type#readme", "license": "MIT", "type": "commonjs", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/babel-plugin-jsx.git"}, "bugs": {"url": "https://github.com/vuejs/babel-plugin-jsx/issues"}, "files": ["dist"], "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dependencies": {"@babel/code-frame": "^7.26.2", "@babel/helper-module-imports": "^7.25.9", "@babel/helper-plugin-utils": "^7.26.5", "@babel/parser": "^7.26.9", "@vue/compiler-sfc": "^3.5.13"}, "devDependencies": {"@babel/core": "^7.26.9", "@types/babel__code-frame": "^7.0.6", "vue": "^3.5.13"}, "scripts": {"build": "tsup", "watch": "tsup --watch"}}