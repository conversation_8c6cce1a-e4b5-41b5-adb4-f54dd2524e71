<template>
  <nav class="navbar" :class="{ 'scrolled': isScrolled }">
    <div class="container">
      <div class="nav-content">
        <!-- Logo -->
        <div class="nav-logo">
          <div class="logo-icon">
            <svg viewBox="0 0 24 24" width="32" height="32">
              <defs>
                <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#667eea"/>
                  <stop offset="100%" style="stop-color:#764ba2"/>
                </linearGradient>
              </defs>
              <path fill="url(#logoGradient)" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
          </div>
          <div class="logo-text">
            <span class="logo-name">EduTrack</span>
            <span class="logo-subtitle">مساعد المعلم</span>
          </div>
        </div>

        <!-- Desktop Navigation -->
        <div class="nav-links desktop-nav">
          <a href="#home" class="nav-link" @click="scrollToSection('home')">
            <i class="nav-icon">🏠</i>
            الرئيسية
          </a>
          <a href="#features" class="nav-link" @click="scrollToSection('features')">
            <i class="nav-icon">✨</i>
            المميزات
          </a>
          <a href="#download" class="nav-link" @click="scrollToSection('download')">
            <i class="nav-icon">📱</i>
            التحميل
          </a>
          <a href="#requirements" class="nav-link" @click="scrollToSection('requirements')">
            <i class="nav-icon">⚙️</i>
            المتطلبات
          </a>
          <a href="#contact" class="nav-link" @click="scrollToSection('contact')">
            <i class="nav-icon">📞</i>
            التواصل
          </a>
        </div>

        <!-- CTA Button -->
        <div class="nav-cta desktop-nav">
          <a href="#download" class="cta-button" @click="scrollToSection('download')">
            <svg viewBox="0 0 24 24" width="20" height="20">
              <path fill="currentColor" d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z"/>
            </svg>
            تحميل مجاني
          </a>
        </div>

        <!-- Mobile Menu Toggle -->
        <button class="mobile-toggle" @click="toggleMobileMenu" :class="{ active: isMobileMenuOpen }">
          <span></span>
          <span></span>
          <span></span>
        </button>
      </div>

      <!-- Mobile Navigation -->
      <div class="mobile-nav" :class="{ open: isMobileMenuOpen }">
        <div class="mobile-nav-content">
          <a href="#home" class="mobile-nav-link" @click="handleMobileNavClick('home')">
            <i class="nav-icon">🏠</i>
            الرئيسية
          </a>
          <a href="#features" class="mobile-nav-link" @click="handleMobileNavClick('features')">
            <i class="nav-icon">✨</i>
            المميزات
          </a>
          <a href="#download" class="mobile-nav-link" @click="handleMobileNavClick('download')">
            <i class="nav-icon">📱</i>
            التحميل
          </a>
          <a href="#requirements" class="mobile-nav-link" @click="handleMobileNavClick('requirements')">
            <i class="nav-icon">⚙️</i>
            المتطلبات
          </a>
          <a href="#contact" class="mobile-nav-link" @click="handleMobileNavClick('contact')">
            <i class="nav-icon">📞</i>
            التواصل
          </a>
          <div class="mobile-cta">
            <a href="#download" class="cta-button" @click="handleMobileNavClick('download')">
              <svg viewBox="0 0 24 24" width="20" height="20">
                <path fill="currentColor" d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z"/>
              </svg>
              تحميل مجاني
            </a>
          </div>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

const isScrolled = ref(false)
const isMobileMenuOpen = ref(false)

const handleScroll = () => {
  isScrolled.value = window.scrollY > 50
}

const scrollToSection = (sectionId) => {
  const element = document.getElementById(sectionId) || document.querySelector(`#${sectionId}`)
  if (element) {
    element.scrollIntoView({ 
      behavior: 'smooth',
      block: 'start'
    })
  }
}

const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

const handleMobileNavClick = (sectionId) => {
  scrollToSection(sectionId)
  isMobileMenuOpen.value = false
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.navbar.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  text-decoration: none;
  color: inherit;
}

.logo-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.logo-text {
  display: flex;
  flex-direction: column;
}

.logo-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
}

.logo-subtitle {
  font-size: 0.8rem;
  color: #667eea;
  font-weight: 500;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  color: #2c3e50;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
}

.nav-link:hover {
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

.nav-icon {
  font-size: 1.1rem;
}

.cta-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.mobile-toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.mobile-toggle span {
  width: 24px;
  height: 3px;
  background: #2c3e50;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.mobile-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.mobile-toggle.active span:nth-child(2) {
  opacity: 0;
}

.mobile-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

.mobile-nav {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  transform: translateY(-100%);
  opacity: 0;
  transition: all 0.3s ease;
}

.mobile-nav.open {
  transform: translateY(0);
  opacity: 1;
}

.mobile-nav-content {
  padding: 1rem 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  gap: 1rem;
  text-decoration: none;
  color: #2c3e50;
  font-weight: 500;
  padding: 1rem 1.5rem;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin: 0 1rem;
}

.mobile-nav-link:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.mobile-cta {
  padding: 1rem 1.5rem;
}

.mobile-cta .cta-button {
  width: 100%;
  justify-content: center;
}

@media (max-width: 768px) {
  .desktop-nav {
    display: none;
  }
  
  .mobile-toggle {
    display: flex;
  }
  
  .mobile-nav {
    display: block;
  }
  
  .logo-text {
    display: none;
  }
  
  .nav-content {
    padding: 0.75rem 0;
  }
}

@media (max-width: 480px) {
  .logo-icon {
    width: 40px;
    height: 40px;
  }
  
  .logo-name {
    font-size: 1.2rem;
  }
}
</style>
