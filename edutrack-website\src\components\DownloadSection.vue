<template>
  <section id="download" class="section download">
    <div class="container">
      <h2 class="section-title fade-in">تحميل التطبيق</h2>
      <p class="download-subtitle fade-in">
        احصل على تطبيق EduTrack الآن واستمتع بتجربة إدارة تعليمية متطورة
      </p>
      
      <div class="download-content">
        <div class="download-cards">
          <div class="download-card android-card fade-in">
            <div class="card-header">
              <div class="platform-icon">
                <svg viewBox="0 0 24 24" width="60" height="60">
                  <path fill="#3DDC84" d="M17.523 15.3414c-.5511 0-.9993-.4486-.9993-.9997s.4482-.9993.9993-.9993c.5511 0 .9993.4482.9993.9993.0001.5511-.4482.9997-.9993.9997m-11.046 0c-.5511 0-.9993-.4486-.9993-.9997s.4482-.9993.9993-.9993c.5511 0 .9993.4482.9993.9993 0 .5511-.4482.9997-.9993.9997m11.4045-6.02l1.9973-3.4592a.416.416 0 00-.1518-.5972.416.416 0 00-.5972.1518l-2.0223 3.503C15.5902 8.2439 13.8533 7.8508 12 7.8508s-3.5902.3931-5.1333 1.0989L4.8442 5.4467a.4161.4161 0 00-.5972-.1518.416.416 0 00-.1518.5972L6.0952 9.321C3.7155 10.7613 2.25 13.1044 2.25 15.7506h19.5c0-2.6462-1.4655-4.9893-3.8455-6.4296z"/>
                </svg>
              </div>
              <h3 class="platform-title">Android</h3>
              <p class="platform-subtitle">للهواتف والأجهزة اللوحية</p>
            </div>
            <div class="card-body">
              <div class="version-info">
                <span class="version">الإصدار 2.1.0</span>
                <span class="size">حجم الملف: 45 MB</span>
              </div>
              <div class="requirements">
                <h4>المتطلبات:</h4>
                <ul>
                  <li>Android 5.0 أو أحدث</li>
                  <li>2 GB RAM أو أكثر</li>
                  <li>100 MB مساحة فارغة</li>
                </ul>
              </div>
              <div class="download-buttons">
                <a href="#" class="btn btn-primary download-btn" @click="downloadAndroid">
                  <span class="btn-icon">📱</span>
                  تحميل للأندرويد
                </a>
                <a href="#" class="btn btn-outline" @click="downloadAPK">
                  <span class="btn-icon">📦</span>
                  تحميل APK مباشر
                </a>
              </div>
            </div>
          </div>
          
          <div class="download-card ios-card fade-in">
            <div class="card-header">
              <div class="platform-icon">
                <svg viewBox="0 0 24 24" width="60" height="60">
                  <path fill="#007AFF" d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 ********** 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                </svg>
              </div>
              <h3 class="platform-title">iOS</h3>
              <p class="platform-subtitle">لأجهزة iPhone و iPad</p>
            </div>
            <div class="card-body">
              <div class="version-info">
                <span class="version">الإصدار 2.1.0</span>
                <span class="size">حجم الملف: 52 MB</span>
              </div>
              <div class="requirements">
                <h4>المتطلبات:</h4>
                <ul>
                  <li>iOS 12.0 أو أحدث</li>
                  <li>متوافق مع iPhone و iPad</li>
                  <li>100 MB مساحة فارغة</li>
                </ul>
              </div>
              <div class="download-buttons">
                <a href="#" class="btn btn-primary download-btn" @click="downloadIOS">
                  <span class="btn-icon">🍎</span>
                  تحميل من App Store
                </a>
                <a href="#" class="btn btn-outline" @click="downloadTestFlight">
                  <span class="btn-icon">✈️</span>
                  TestFlight Beta
                </a>
              </div>
            </div>
          </div>
        </div>
        
        <div class="download-stats fade-in">
          <div class="stat-item">
            <div class="stat-number">50K+</div>
            <div class="stat-label">تحميل</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">4.8</div>
            <div class="stat-label">تقييم</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">1000+</div>
            <div class="stat-label">معلم</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">25K+</div>
            <div class="stat-label">طالب</div>
          </div>
        </div>
        
        <div class="qr-section fade-in">
          <h3>امسح الكود للتحميل السريع</h3>
          <div class="qr-codes">
            <div class="qr-item">
              <div class="qr-code">
                <div class="qr-placeholder">QR</div>
              </div>
              <span>Android</span>
            </div>
            <div class="qr-item">
              <div class="qr-code">
                <div class="qr-placeholder">QR</div>
              </div>
              <span>iOS</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
const downloadAndroid = () => {
  // هنا يمكن إضافة رابط التحميل الفعلي
  alert('سيتم توجيهك إلى Google Play Store')
}

const downloadAPK = () => {
  // هنا يمكن إضافة رابط تحميل APK المباشر
  alert('سيتم تحميل ملف APK مباشرة')
}

const downloadIOS = () => {
  // هنا يمكن إضافة رابط App Store
  alert('سيتم توجيهك إلى App Store')
}

const downloadTestFlight = () => {
  // هنا يمكن إضافة رابط TestFlight
  alert('سيتم توجيهك إلى TestFlight')
}
</script>

<style scoped>
.download {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.download::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.section-title {
  color: white !important;
}

.download-subtitle {
  text-align: center;
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 4rem;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.download-content {
  width: 100%;
  margin: 0;
  position: relative;
  z-index: 2;
  padding: 0;
}

.download-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 3rem;
  margin-bottom: 4rem;
  width: 100%;
  margin: 0 0 4rem 0;
  padding: 0;
}

.download-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 3rem;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #2c3e50;
}

.download-card:hover {
  transform: translateY(-8px);
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

.card-header {
  text-align: center;
  margin-bottom: 2rem;
}

.platform-icon {
  margin-bottom: 1rem;
}

.platform-title {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #2c3e50;
}

.platform-subtitle {
  color: #666;
  font-size: 1rem;
}

.version-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 10px;
}

.version, .size {
  font-weight: 600;
  color: #495057;
}

.requirements h4 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-weight: 600;
}

.requirements ul {
  list-style: none;
  padding: 0;
  margin-bottom: 2rem;
}

.requirements li {
  padding: 0.5rem 0;
  color: #666;
  position: relative;
  padding-right: 1.5rem;
}

.requirements li::before {
  content: '✓';
  position: absolute;
  right: 0;
  color: #28a745;
  font-weight: bold;
}

.download-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.btn-outline {
  background: transparent;
  border: 2px solid #667eea;
  color: #667eea;
}

.btn-outline:hover {
  background: #667eea;
  color: white;
}

.btn-icon {
  margin-left: 8px;
  font-size: 1.1rem;
}

.download-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  margin-bottom: 4rem;
  padding: 3rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 3rem;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: #6c757d;
  font-weight: 600;
  font-size: 1.1rem;
}

.qr-section {
  text-align: center;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  padding: 3rem;
  border-radius: 24px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.qr-section h3 {
  margin-bottom: 2.5rem;
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 700;
}

.qr-codes {
  display: flex;
  justify-content: center;
  gap: 4rem;
}

.qr-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.qr-code {
  width: 140px;
  height: 140px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px solid #dee2e6;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: #6c757d;
  transition: all 0.3s ease;
}

.qr-code:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.qr-placeholder {
  font-size: 1.8rem;
}

@media (max-width: 768px) {
  .download-cards {
    grid-template-columns: 1fr;
  }
  
  .download-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .qr-codes {
    flex-direction: column;
    gap: 2rem;
  }
  
  .download-card {
    padding: 1.5rem;
  }
}
</style>
