<template>
  <section class="section contact">
    <div class="container">
      <h2 class="section-title fade-in">الدعم والتواصل</h2>
      <p class="contact-subtitle fade-in">
        نحن هنا لمساعدتك! تواصل معنا للحصول على الدعم أو الإبلاغ عن مشكلة
      </p>
      
      <div class="contact-content">
        <div class="contact-info fade-in">
          <div class="contact-card">
            <div class="contact-icon">
              <svg viewBox="0 0 24 24" width="40" height="40">
                <path fill="#667eea" d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
              </svg>
            </div>
            <h3>البريد الإلكتروني</h3>
            <p>للدعم التقني والاستفسارات</p>
            <a href="mailto:<EMAIL>" class="contact-link">
              <EMAIL>
            </a>
          </div>
          
          <div class="contact-card">
            <div class="contact-icon">
              <svg viewBox="0 0 24 24" width="40" height="40">
                <path fill="#667eea" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </div>
            <h3>الدعم السريع</h3>
            <p>للمساعدة الفورية والحلول السريعة</p>
            <div class="response-time">
              <span class="time-badge">⏱️ الرد خلال 24 ساعة</span>
            </div>
          </div>
          
          <div class="contact-card">
            <div class="contact-icon">
              <svg viewBox="0 0 24 24" width="40" height="40">
                <path fill="#667eea" d="M9 11H7v2h2v-2zm4 0h-2v2h2v-2zm4 0h-2v2h2v-2zm2-7h-1V2h-2v2H8V2H6v2H5c-1.1 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11z"/>
              </svg>
            </div>
            <h3>ساعات العمل</h3>
            <p>أوقات توفر فريق الدعم</p>
            <div class="working-hours">
              <div class="hour-item">
                <span>السبت - الخميس</span>
                <span>9:00 ص - 6:00 م</span>
              </div>
              <div class="hour-item">
                <span>الجمعة</span>
                <span>مغلق</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="contact-form-section fade-in">
          <div class="form-container">
            <h3>أرسل لنا رسالة</h3>
            <form @submit.prevent="submitForm" class="contact-form">
              <div class="form-group">
                <label for="name">الاسم الكامل</label>
                <input 
                  type="text" 
                  id="name" 
                  v-model="form.name" 
                  required 
                  placeholder="أدخل اسمك الكامل"
                >
              </div>
              
              <div class="form-group">
                <label for="email">البريد الإلكتروني</label>
                <input 
                  type="email" 
                  id="email" 
                  v-model="form.email" 
                  required 
                  placeholder="أدخل بريدك الإلكتروني"
                >
              </div>
              
              <div class="form-group">
                <label for="subject">الموضوع</label>
                <select id="subject" v-model="form.subject" required>
                  <option value="">اختر الموضوع</option>
                  <option value="support">دعم تقني</option>
                  <option value="bug">الإبلاغ عن خطأ</option>
                  <option value="feature">طلب ميزة جديدة</option>
                  <option value="general">استفسار عام</option>
                </select>
              </div>
              
              <div class="form-group">
                <label for="message">الرسالة</label>
                <textarea 
                  id="message" 
                  v-model="form.message" 
                  required 
                  rows="5"
                  placeholder="اكتب رسالتك هنا..."
                ></textarea>
              </div>
              
              <button type="submit" class="btn btn-primary submit-btn" :disabled="isSubmitting">
                <span v-if="!isSubmitting">
                  <span class="btn-icon">📧</span>
                  إرسال الرسالة
                </span>
                <span v-else>
                  <span class="btn-icon">⏳</span>
                  جاري الإرسال...
                </span>
              </button>
            </form>
            
            <div v-if="submitMessage" class="submit-message" :class="submitStatus">
              {{ submitMessage }}
            </div>
          </div>
        </div>
      </div>
      
      <div class="faq-section fade-in">
        <h3>الأسئلة الشائعة</h3>
        <div class="faq-grid">
          <div class="faq-item" v-for="(faq, index) in faqs" :key="index">
            <div class="faq-question" @click="toggleFaq(index)">
              <h4>{{ faq.question }}</h4>
              <span class="faq-toggle" :class="{ active: faq.isOpen }">+</span>
            </div>
            <div class="faq-answer" :class="{ open: faq.isOpen }">
              <p>{{ faq.answer }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, reactive } from 'vue'

const form = reactive({
  name: '',
  email: '',
  subject: '',
  message: ''
})

const isSubmitting = ref(false)
const submitMessage = ref('')
const submitStatus = ref('')

const faqs = ref([
  {
    question: 'كيف يمكنني تحميل التطبيق؟',
    answer: 'يمكنك تحميل التطبيق من Google Play Store للأندرويد أو App Store لأجهزة iOS. كما يمكنك تحميل ملف APK مباشرة للأندرويد.',
    isOpen: false
  },
  {
    question: 'هل التطبيق مجاني؟',
    answer: 'نعم، تطبيق EduTrack مجاني تماماً مع جميع المميزات الأساسية. لا توجد رسوم خفية أو اشتراكات مطلوبة.',
    isOpen: false
  },
  {
    question: 'كيف يمكنني عمل نسخة احتياطية من بياناتي؟',
    answer: 'يمكنك إنشاء نسخة احتياطية من خلال الذهاب إلى الإعدادات > النسخ الاحتياطي > إنشاء نسخة احتياطية. سيتم حفظ الملف في مجلد التحميلات.',
    isOpen: false
  },
  {
    question: 'ماذا أفعل إذا نسيت كلمة المرور؟',
    answer: 'التطبيق لا يستخدم كلمات مرور حالياً. جميع البيانات محفوظة محلياً على جهازك. إذا كنت تواجه مشاكل في الوصول، تواصل معنا.',
    isOpen: false
  },
  {
    question: 'هل يمكنني استخدام التطبيق على أكثر من جهاز؟',
    answer: 'نعم، يمكنك تثبيت التطبيق على عدة أجهزة. لنقل البيانات، استخدم ميزة النسخ الاحتياطي واستعادة البيانات.',
    isOpen: false
  },
  {
    question: 'كيف يمكنني الإبلاغ عن خطأ في التطبيق؟',
    answer: 'يمكنك الإبلاغ عن الأخطاء من خلال نموذج التواصل أعلاه أو إرسال بريد إلكتروني مباشر. يرجى تضمين تفاصيل الخطأ ونوع الجهاز.',
    isOpen: false
  }
])

const submitForm = async () => {
  isSubmitting.value = true
  submitMessage.value = ''
  
  try {
    // محاكاة إرسال النموذج
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    submitMessage.value = 'تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.'
    submitStatus.value = 'success'
    
    // إعادة تعيين النموذج
    Object.keys(form).forEach(key => form[key] = '')
    
  } catch (error) {
    submitMessage.value = 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.'
    submitStatus.value = 'error'
  } finally {
    isSubmitting.value = false
    
    // إخفاء الرسالة بعد 5 ثوان
    setTimeout(() => {
      submitMessage.value = ''
    }, 5000)
  }
}

const toggleFaq = (index) => {
  faqs.value[index].isOpen = !faqs.value[index].isOpen
}
</script>

<style scoped>
.contact {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.contact::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.3;
}

.section-title {
  color: white !important;
}

.contact-subtitle {
  text-align: center;
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 4rem;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-bottom: 5rem;
  position: relative;
  z-index: 2;
  width: 100%;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.contact-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  padding: 2.5rem;
  border-radius: 20px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  text-align: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #2c3e50;
}

.contact-card:hover {
  transform: translateY(-8px);
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

.contact-icon {
  margin-bottom: 1rem;
}

.contact-card h3 {
  font-size: 1.3rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.contact-card p {
  color: #666;
  margin-bottom: 1rem;
}

.contact-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
  word-break: break-all;
}

.contact-link:hover {
  text-decoration: underline;
}

.time-badge {
  background: #e8f5e8;
  color: #28a745;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.working-hours {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.hour-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.form-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  padding: 3rem;
  border-radius: 20px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.form-container h3 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 2.5rem;
  text-align: center;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
}

.submit-btn {
  width: 100%;
  padding: 1rem;
  font-size: 1.1rem;
}

.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.submit-message {
  margin-top: 1rem;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
  font-weight: 600;
}

.submit-message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.submit-message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.faq-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  padding: 4rem;
  border-radius: 24px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 2;
}

.faq-section h3 {
  font-size: 2.2rem;
  font-weight: 700;
  color: #2c3e50;
  text-align: center;
  margin-bottom: 3rem;
}

.faq-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.faq-item {
  border: 1px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: #f8f9fa;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.faq-question:hover {
  background: #e9ecef;
}

.faq-question h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.faq-toggle {
  font-size: 1.5rem;
  font-weight: bold;
  color: #667eea;
  transition: transform 0.3s ease;
}

.faq-toggle.active {
  transform: rotate(45deg);
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.faq-answer.open {
  max-height: 200px;
}

.faq-answer p {
  padding: 1.5rem;
  margin: 0;
  color: #666;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .contact-content {
    grid-template-columns: 1fr;
  }
  
  .form-container {
    padding: 1.5rem;
  }
  
  .faq-section {
    padding: 2rem;
  }
}
</style>
