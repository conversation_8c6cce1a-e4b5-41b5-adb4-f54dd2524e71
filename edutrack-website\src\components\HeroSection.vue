<template>
  <section class="hero">
    <div class="container">
      <div class="hero-content">
        <div class="hero-text fade-in">
          <div class="hero-badge">
            <svg viewBox="0 0 24 24" width="16" height="16">
              <path fill="currentColor" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            الأفضل في إدارة التعليم
          </div>
          <h1 class="hero-title">
            تطبيق <span class="highlight">EduTrack</span>
            <br>
            <span class="subtitle">مساعد المعلم المستقبلي</span>
          </h1>
          <p class="hero-description">
            تطبيق متطور مصمم خصيصاً للمعلمين لمساعدتهم في إدارة الفصول الدراسية والمجموعات التعليمية بكفاءة عالية.
            يوفر التطبيق واجهة مستخدم عصرية وسلسة مع تحسينات أداء متقدمة لضمان تجربة مستخدم سريعة وفعالة.
          </p>
          <div class="hero-stats">
            <div class="stat-item">
              <div class="stat-number">50K+</div>
              <div class="stat-label">تحميل</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">4.8</div>
              <div class="stat-label">تقييم</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">1000+</div>
              <div class="stat-label">معلم</div>
            </div>
          </div>
          <div class="hero-buttons">
            <a href="#download" class="btn btn-primary">
              <svg viewBox="0 0 24 24" width="20" height="20">
                <path fill="currentColor" d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z"/>
              </svg>
              تحميل مجاني
            </a>
            <a href="#features" class="btn btn-outline">
              <svg viewBox="0 0 24 24" width="20" height="20">
                <path fill="currentColor" d="M13,9H11V7H13M13,17H11V11H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
              </svg>
              اكتشف المميزات
            </a>
          </div>
        </div>
        <div class="hero-visual fade-in">
          <div class="phone-mockup">
            <div class="phone-frame">
              <div class="phone-screen">
                <div class="app-preview">
                  <div class="app-header">
                    <div class="header-content">
                      <div class="app-logo">
                        <svg viewBox="0 0 24 24" width="24" height="24">
                          <path fill="#667eea" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                      </div>
                      <div class="app-info">
                        <div class="app-title">EduTrack</div>
                        <div class="app-subtitle">مساعد المعلم</div>
                      </div>
                    </div>
                  </div>
                  <div class="app-stats">
                    <div class="stat-card">
                      <div class="stat-icon">👥</div>
                      <div class="stat-content">
                        <div class="stat-number">12</div>
                        <div class="stat-label">مجموعة</div>
                      </div>
                    </div>
                    <div class="stat-card">
                      <div class="stat-icon">👨‍🎓</div>
                      <div class="stat-content">
                        <div class="stat-number">156</div>
                        <div class="stat-label">طالب</div>
                      </div>
                    </div>
                    <div class="stat-card">
                      <div class="stat-icon">📚</div>
                      <div class="stat-content">
                        <div class="stat-number">8</div>
                        <div class="stat-label">درس اليوم</div>
                      </div>
                    </div>
                  </div>
                  <div class="app-features">
                    <div class="feature-item">
                      <div class="feature-icon">
                        <svg viewBox="0 0 24 24" width="20" height="20">
                          <path fill="#667eea" d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2M4 1h2v6h2.5l-3.5 7-3.5-7H4V1M16 13c1.11 0 2-.89 2-2s-.89-2-2-2-2 .89-2 2 .89 2 2 2m1 1.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5"/>
                        </svg>
                      </div>
                      <span class="feature-text">إدارة المجموعات</span>
                    </div>
                    <div class="feature-item">
                      <div class="feature-icon">
                        <svg viewBox="0 0 24 24" width="20" height="20">
                          <path fill="#667eea" d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
                        </svg>
                      </div>
                      <span class="feature-text">جدولة الدروس</span>
                    </div>
                    <div class="feature-item">
                      <div class="feature-icon">
                        <svg viewBox="0 0 24 24" width="20" height="20">
                          <path fill="#667eea" d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                        </svg>
                      </div>
                      <span class="feature-text">تسجيل الحضور</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="floating-elements">
            <div class="floating-card card-1">
              <div class="card-icon">📊</div>
              <div class="card-text">تحليلات متقدمة</div>
            </div>
            <div class="floating-card card-2">
              <div class="card-icon">🔒</div>
              <div class="card-text">أمان عالي</div>
            </div>
            <div class="floating-card card-3">
              <div class="card-icon">⚡</div>
              <div class="card-text">أداء سريع</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="hero-background">
      <div class="bg-gradient"></div>
      <div class="bg-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
      </div>
    </div>
  </section>
</template>

<style scoped>
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  overflow: hidden;
  padding-top: 100px;
  width: 100%;
}

.hero-content {
  display: grid;
  grid-template-columns: 1.2fr 1fr;
  gap: 6rem;
  align-items: center;
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: none;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-title {
  font-size: 4.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 2rem;
}

.highlight {
  background: linear-gradient(45deg, #f093fb, #f5576c);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.subtitle {
  font-size: 3rem;
  font-weight: 600;
  opacity: 0.9;
}

.hero-description {
  font-size: 1.3rem;
  line-height: 1.8;
  margin-bottom: 2.5rem;
  opacity: 0.9;
  max-width: 600px;
}

.hero-stats {
  display: flex;
  gap: 3rem;
  margin-bottom: 3rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: #f093fb;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1rem;
  opacity: 0.8;
}

.hero-buttons {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.hero-visual {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.phone-mockup {
  position: relative;
  z-index: 2;
}

.phone-frame {
  width: 320px;
  height: 640px;
  background: linear-gradient(145deg, #2c3e50, #34495e);
  border-radius: 40px;
  padding: 8px;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  position: relative;
}

.phone-frame::before {
  content: '';
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 6px;
  background: #34495e;
  border-radius: 3px;
}

.phone-screen {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 32px;
  overflow: hidden;
  position: relative;
}

.app-preview {
  padding: 24px;
  height: 100%;
  color: #2c3e50;
}

.app-header {
  margin-bottom: 32px;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 12px;
  background: white;
  padding: 16px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.app-logo {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.app-info {
  flex: 1;
}

.app-title {
  font-size: 1.2rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 2px;
}

.app-subtitle {
  font-size: 0.85rem;
  color: #667eea;
  font-weight: 500;
}

.app-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 12px;
  background: white;
  padding: 16px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 1.8rem;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 1.4rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 0.8rem;
  color: #6c757d;
}

.app-features {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  background: white;
  padding: 16px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-2px);
}

.feature-icon {
  width: 36px;
  height: 36px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-text {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-card {
  position: absolute;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 1rem 1.5rem;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #2c3e50;
  font-weight: 600;
  font-size: 0.9rem;
  animation: floatCard 6s ease-in-out infinite;
}

.card-1 {
  top: 15%;
  right: -10%;
  animation-delay: 0s;
}

.card-2 {
  bottom: 30%;
  left: -15%;
  animation-delay: 2s;
}

.card-3 {
  top: 50%;
  right: -5%;
  animation-delay: 4s;
}

.card-icon {
  font-size: 1.5rem;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow: hidden;
}

.bg-gradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  opacity: 0.9;
}

.bg-shapes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 8s ease-in-out infinite;
}

.shape-1 {
  width: 300px;
  height: 300px;
  top: 10%;
  right: 5%;
  animation-delay: 0s;
}

.shape-2 {
  width: 200px;
  height: 200px;
  bottom: 15%;
  left: 10%;
  animation-delay: 2s;
}

.shape-3 {
  width: 150px;
  height: 150px;
  top: 50%;
  right: 15%;
  animation-delay: 4s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  bottom: 40%;
  left: 5%;
  animation-delay: 6s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-30px) rotate(180deg);
  }
}

@keyframes floatCard {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@media (max-width: 1024px) {
  .hero-content {
    gap: 4rem;
  }

  .hero-title {
    font-size: 3.5rem;
  }

  .floating-card {
    display: none;
  }
}

@media (max-width: 768px) {
  .hero {
    padding-top: 80px;
    min-height: auto;
    padding-bottom: 4rem;
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .hero-title {
    font-size: 2.8rem;
  }

  .subtitle {
    font-size: 2.2rem;
  }

  .hero-description {
    font-size: 1.1rem;
    max-width: none;
  }

  .hero-stats {
    justify-content: center;
    gap: 2rem;
  }

  .phone-frame {
    width: 280px;
    height: 560px;
  }

  .hero-buttons {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2.2rem;
  }

  .subtitle {
    font-size: 1.8rem;
  }

  .hero-stats {
    gap: 1.5rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .phone-frame {
    width: 250px;
    height: 500px;
  }
}
</style>
