{"name": "run-applescript", "version": "7.0.0", "description": "Run AppleScript and get the result", "license": "MIT", "repository": "sindresorhus/run-applescript", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["macos", "mac", "applescript", "osascript", "run", "execute"], "devDependencies": {"ava": "^6.0.1", "tsd": "^0.30.0", "xo": "^0.56.0"}}