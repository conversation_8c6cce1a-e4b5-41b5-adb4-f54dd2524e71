<template>
  <section class="section requirements">
    <div class="container">
      <h2 class="section-title fade-in">متطلبات النظام</h2>
      <p class="requirements-subtitle fade-in">
        تأكد من أن جهازك يلبي المتطلبات التالية للحصول على أفضل تجربة مع EduTrack
      </p>
      
      <div class="requirements-content">
        <div class="requirements-grid">
          <!-- Android Requirements -->
          <div class="requirement-card android-req fade-in">
            <div class="card-header">
              <div class="platform-icon">
                <svg viewBox="0 0 24 24" width="50" height="50">
                  <path fill="#3DDC84" d="M17.523 15.3414c-.5511 0-.9993-.4486-.9993-.9997s.4482-.9993.9993-.9993c.5511 0 .9993.4482.9993.9993.0001.5511-.4482.9997-.9993.9997m-11.046 0c-.5511 0-.9993-.4486-.9993-.9997s.4482-.9993.9993-.9993c.5511 0 .9993.4482.9993.9993 0 .5511-.4482.9997-.9993.9997m11.4045-6.02l1.9973-3.4592a.416.416 0 00-.1518-.5972.416.416 0 00-.5972.1518l-2.0223 3.503C15.5902 8.2439 13.8533 7.8508 12 7.8508s-3.5902.3931-5.1333 1.0989L4.8442 5.4467a.4161.4161 0 00-.5972-.1518.416.416 0 00-.1518.5972L6.0952 9.321C3.7155 10.7613 2.25 13.1044 2.25 15.7506h19.5c0-2.6462-1.4655-4.9893-3.8455-6.4296z"/>
                </svg>
              </div>
              <h3 class="platform-title">متطلبات Android</h3>
            </div>
            <div class="requirements-list">
              <div class="requirement-item">
                <div class="req-icon">📱</div>
                <div class="req-content">
                  <h4>نظام التشغيل</h4>
                  <p>Android 5.0 (Lollipop) أو أحدث</p>
                  <span class="req-note">API Level 21+</span>
                </div>
              </div>
              <div class="requirement-item">
                <div class="req-icon">🧠</div>
                <div class="req-content">
                  <h4>ذاكرة الوصول العشوائي</h4>
                  <p>2 GB RAM أو أكثر</p>
                  <span class="req-note">يُنصح بـ 3 GB للأداء الأمثل</span>
                </div>
              </div>
              <div class="requirement-item">
                <div class="req-icon">💾</div>
                <div class="req-content">
                  <h4>مساحة التخزين</h4>
                  <p>100 MB مساحة فارغة على الأقل</p>
                  <span class="req-note">للتطبيق والبيانات</span>
                </div>
              </div>
              <div class="requirement-item">
                <div class="req-icon">🔧</div>
                <div class="req-content">
                  <h4>معالج الرسوميات</h4>
                  <p>Adreno 306, Mali-T720 أو أفضل</p>
                  <span class="req-note">لدعم الواجهة المتقدمة</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- iOS Requirements -->
          <div class="requirement-card ios-req fade-in">
            <div class="card-header">
              <div class="platform-icon">
                <svg viewBox="0 0 24 24" width="50" height="50">
                  <path fill="#007AFF" d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                </svg>
              </div>
              <h3 class="platform-title">متطلبات iOS</h3>
            </div>
            <div class="requirements-list">
              <div class="requirement-item">
                <div class="req-icon">📱</div>
                <div class="req-content">
                  <h4>نظام التشغيل</h4>
                  <p>iOS 12.0 أو أحدث</p>
                  <span class="req-note">متوافق مع iPhone و iPad</span>
                </div>
              </div>
              <div class="requirement-item">
                <div class="req-icon">📱</div>
                <div class="req-content">
                  <h4>الأجهزة المدعومة</h4>
                  <p>iPhone 6s أو أحدث</p>
                  <span class="req-note">iPad Air 2 أو أحدث</span>
                </div>
              </div>
              <div class="requirement-item">
                <div class="req-icon">💾</div>
                <div class="req-content">
                  <h4>مساحة التخزين</h4>
                  <p>100 MB مساحة فارغة على الأقل</p>
                  <span class="req-note">للتطبيق والبيانات</span>
                </div>
              </div>
              <div class="requirement-item">
                <div class="req-icon">🔧</div>
                <div class="req-content">
                  <h4>معالج الرسوميات</h4>
                  <p>A9 Bionic أو أحدث</p>
                  <span class="req-note">لدعم الواجهة المتقدمة</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Performance Tips -->
        <div class="performance-tips fade-in">
          <h3 class="tips-title">نصائح لتحسين الأداء</h3>
          <div class="tips-grid">
            <div class="tip-card">
              <div class="tip-icon">⚡</div>
              <h4>تحسين الذاكرة</h4>
              <p>أغلق التطبيقات غير المستخدمة لتحرير ذاكرة إضافية</p>
            </div>
            <div class="tip-card">
              <div class="tip-icon">🔄</div>
              <h4>التحديثات</h4>
              <p>حافظ على تحديث التطبيق لأحدث إصدار للحصول على أفضل أداء</p>
            </div>
            <div class="tip-card">
              <div class="tip-icon">🧹</div>
              <h4>تنظيف البيانات</h4>
              <p>استخدم ميزة تنظيف البيانات القديمة من وقت لآخر</p>
            </div>
            <div class="tip-card">
              <div class="tip-icon">💾</div>
              <h4>النسخ الاحتياطي</h4>
              <p>قم بعمل نسخ احتياطي دوري لحماية بياناتك</p>
            </div>
          </div>
        </div>
        
        <!-- Compatibility Check -->
        <div class="compatibility-check fade-in">
          <h3>فحص التوافق</h3>
          <p>تحقق من توافق جهازك مع التطبيق</p>
          <button class="btn btn-primary check-btn" @click="checkCompatibility">
            <span class="btn-icon">🔍</span>
            فحص التوافق
          </button>
          <div v-if="compatibilityResult" class="compatibility-result" :class="compatibilityResult.status">
            <div class="result-icon">{{ compatibilityResult.icon }}</div>
            <div class="result-text">
              <h4>{{ compatibilityResult.title }}</h4>
              <p>{{ compatibilityResult.message }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref } from 'vue'

const compatibilityResult = ref(null)

const checkCompatibility = () => {
  // محاكاة فحص التوافق
  const userAgent = navigator.userAgent
  const isAndroid = /Android/i.test(userAgent)
  const isIOS = /iPhone|iPad|iPod/i.test(userAgent)
  
  setTimeout(() => {
    if (isAndroid || isIOS) {
      compatibilityResult.value = {
        status: 'compatible',
        icon: '✅',
        title: 'جهازك متوافق!',
        message: 'يمكنك تحميل وتشغيل التطبيق بدون مشاكل'
      }
    } else {
      compatibilityResult.value = {
        status: 'unknown',
        icon: '❓',
        title: 'لا يمكن تحديد التوافق',
        message: 'يرجى التحقق من متطلبات النظام يدوياً'
      }
    }
  }, 1000)
}
</script>

<style scoped>
.requirements {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
}

.requirements::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 300px;
  background: linear-gradient(180deg, rgba(102, 126, 234, 0.03) 0%, transparent 100%);
}

.requirements-subtitle {
  text-align: center;
  font-size: 1.3rem;
  color: #6c757d;
  margin-bottom: 4rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.requirements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 3rem;
  margin-bottom: 4rem;
  position: relative;
  z-index: 2;
  width: 100%;
}

.requirement-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 3rem;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.requirement-card:hover {
  transform: translateY(-8px);
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.3);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f8f9fa;
}

.platform-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c3e50;
}

.requirements-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.requirement-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.requirement-item:hover {
  background: #e9ecef;
  transform: translateX(-5px);
}

.req-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.req-content h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.req-content p {
  color: #495057;
  margin-bottom: 0.25rem;
}

.req-note {
  font-size: 0.9rem;
  color: #6c757d;
  font-style: italic;
}

.performance-tips {
  background: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.tips-title {
  text-align: center;
  font-size: 1.8rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 2rem;
}

.tips-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.tip-card {
  text-align: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.tip-card:hover {
  background: #e9ecef;
  transform: translateY(-3px);
}

.tip-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.tip-card h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.tip-card p {
  color: #666;
  line-height: 1.5;
}

.compatibility-check {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 20px;
  text-align: center;
}

.compatibility-check h3 {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.compatibility-check p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.check-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.check-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.compatibility-result {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
  padding: 1.5rem;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.result-icon {
  font-size: 2rem;
}

.result-text h4 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.result-text p {
  opacity: 0.9;
}

.compatible {
  border: 2px solid #28a745;
}

.unknown {
  border: 2px solid #ffc107;
}

@media (max-width: 768px) {
  .requirements-grid {
    grid-template-columns: 1fr;
  }
  
  .tips-grid {
    grid-template-columns: 1fr;
  }
  
  .requirement-card {
    padding: 1.5rem;
  }
  
  .compatibility-result {
    flex-direction: column;
    text-align: center;
  }
}
</style>
