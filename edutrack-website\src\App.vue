<script setup>
import NavBar from './components/NavBar.vue'
import HeroSection from './components/HeroSection.vue'
import FeaturesSection from './components/FeaturesSection.vue'
import DownloadSection from './components/DownloadSection.vue'
import SystemRequirements from './components/SystemRequirements.vue'
import ContactSection from './components/ContactSection.vue'
import FooterSection from './components/FooterSection.vue'
</script>

<template>
  <div class="app">
    <!-- Navigation Bar -->
    <NavBar />

    <!-- Hero Section -->
    <section id="home">
      <HeroSection />
    </section>

    <!-- Features Section -->
    <section id="features">
      <FeaturesSection />
    </section>

    <!-- Download Section -->
    <section id="download">
      <DownloadSection />
    </section>

    <!-- System Requirements -->
    <section id="requirements">
      <SystemRequirements />
    </section>

    <!-- Contact Section -->
    <section id="contact">
      <ContactSection />
    </section>

    <!-- Footer -->
    <FooterSection />
  </div>
</template>

<style>
/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  width: 100%;
}

body {
  font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.7;
  color: #2c3e50;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  direction: rtl;
  overflow-x: hidden;
  width: 100%;
  margin: 0;
  padding: 0;
}

.app {
  min-height: 100vh;
  position: relative;
  width: 100%;
  margin: 0;
  padding: 0;
}

/* Container */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
  box-sizing: border-box;
}

/* Section Styles */
.section {
  padding: 120px 0;
  position: relative;
  width: 100%;
}

.section-title {
  font-size: 3.5rem;
  font-weight: 800;
  text-align: center;
  margin-bottom: 4rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -1rem;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

/* Modern Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border-radius: 16px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  box-shadow: 0 8px 32px rgba(240, 147, 251, 0.3);
}

.btn-secondary:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px rgba(240, 147, 251, 0.4);
}

.btn-outline {
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
  border: 2px solid #667eea;
  backdrop-filter: blur(10px);
}

.btn-outline:hover {
  background: #667eea;
  color: white;
  transform: translateY(-4px);
}

/* Animation Classes */
.fade-in {
  animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(60px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Glass Effect */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Full Width Sections */
section {
  width: 100%;
  margin: 0;
  padding: 0;
}

section .container {
  width: 100%;
  max-width: 1400px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .container {
    padding: 0 1.5rem;
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .section {
    padding: 80px 0;
  }

  .section-title {
    font-size: 2.5rem;
    margin-bottom: 3rem;
  }

  .container {
    padding: 0 1rem;
    max-width: 100%;
    width: 100%;
  }

  .btn {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .section {
    padding: 60px 0;
  }

  .section-title {
    font-size: 2rem;
  }
}

/* Scroll Padding for Fixed Navbar */
html {
  scroll-padding-top: 100px;
}
</style>
